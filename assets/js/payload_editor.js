/**
 * Payload Editor JavaScript utilities
 * Handles cursor position insertion and other editor enhancements
 */

export const PayloadEditor = {
  mounted() {
    console.log('PayloadEditor mounted on element:', this.el);
    console.log('PayloadEditor element ID:', this.el.id);
    console.log('PayloadEditor element classes:', this.el.className);

    // Prevent duplicate event handlers
    if (this._handlersInitialized) {
      console.log('PayloadEditor handlers already initialized, skipping');
      return;
    }
    this._handlersInitialized = true;

    // Handle insert at cursor events
    this.handleEvent("insert_at_cursor", ({ target_id, text }) => {
      console.log('insert_at_cursor event received:', { target_id, text });

      // Only handle events for textareas within this hook's element
      const textarea = this.el.querySelector(`#${target_id}`);
      if (!textarea) {
        console.log(`Textarea ${target_id} not found in this hook's element, ignoring event`);
        return;
      }

      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const currentValue = textarea.value;

      // Insert text at cursor position
      const newValue = currentValue.substring(0, start) + text + currentValue.substring(end);
      textarea.value = newValue;

      // Update cursor position
      const newCursorPos = start + text.length;
      textarea.setSelectionRange(newCursorPos, newCursorPos);

      // Focus back to textarea
      textarea.focus();

      // Don't trigger any additional events - let the natural phx-change handle it
      // The textarea already has phx-change="payload_changed" which will trigger automatically
      console.log('Template inserted, letting natural phx-change handle the update');
    });

    // Add syntax highlighting hints (optional enhancement)
    this.addSyntaxHighlighting();

    // Add file upload handling
    this.addFileUploadHandling();
  },

  destroyed() {
    console.log('PayloadEditor destroyed');
    this._handlersInitialized = false;
    if (this._inputTimeout) {
      clearTimeout(this._inputTimeout);
    }
  },

  addSyntaxHighlighting() {
    // Simple syntax highlighting for template syntax
    // Only target textareas within this hook's element
    const textareas = this.el.querySelectorAll('[id^="payload-editor-"]');

    textareas.forEach(textarea => {
      // Avoid duplicate event listeners
      if (textarea._syntaxHighlightingAdded) {
        return;
      }
      textarea._syntaxHighlightingAdded = true;

      const handleInput = (e) => {
        // Add visual feedback for template syntax
        const value = e.target.value;
        const hasTemplate = value.includes('{{') || value.includes('{%');

        if (hasTemplate) {
          e.target.classList.add('template-syntax');
        } else {
          e.target.classList.remove('template-syntax');
        }
      };

      textarea.addEventListener('input', handleInput);

      // Store reference for cleanup
      textarea._syntaxHighlightingHandler = handleInput;
    });
  },

  // Utility function to insert text at cursor
  insertAtCursor(textarea, text) {
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const currentValue = textarea.value;

    const newValue = currentValue.substring(0, start) + text + currentValue.substring(end);
    textarea.value = newValue;

    const newCursorPos = start + text.length;
    textarea.setSelectionRange(newCursorPos, newCursorPos);
    textarea.focus();

    // Trigger change event
    textarea.dispatchEvent(new Event('input', { bubbles: true }));
  },

  // Enhanced features for future implementation
  addAutoComplete() {
    // TODO: Add autocomplete for template functions
    // This could show a dropdown with available functions as user types
  },

  addBracketMatching() {
    // TODO: Add automatic bracket matching for {{ }}
    // When user types {{, automatically add }}
  },

  addLineNumbers() {
    // TODO: Add line numbers for larger payloads
  },

  addFileUploadHandling() {
    console.log('PayloadEditor: Setting up file upload handling');

    // Handle file input changes
    const fileInputs = this.el.querySelectorAll('input[type="file"]');
    console.log('PayloadEditor: Found', fileInputs.length, 'file inputs');

    fileInputs.forEach(fileInput => {
      console.log('PayloadEditor: Setting up file input:', fileInput.id);

      // Avoid duplicate event listeners
      if (fileInput._fileUploadHandlerAdded) {
        console.log('PayloadEditor: File upload handler already added for:', fileInput.id);
        return;
      }
      fileInput._fileUploadHandlerAdded = true;

      fileInput.addEventListener('change', (event) => {
        console.log('PayloadEditor: File input changed');
        const file = event.target.files[0];
        if (!file) {
          console.log('PayloadEditor: No file selected');
          return;
        }

        console.log('PayloadEditor: File selected:', file.name, file.size, file.type);

        // Read file as base64
        const reader = new FileReader();
        reader.onload = (e) => {
          console.log('PayloadEditor: File read successfully');
          const base64Content = e.target.result.split(',')[1]; // Remove data:type;base64, prefix

          const fileData = {
            name: file.name,
            size: file.size,
            type: file.type,
            content: base64Content
          };

          console.log('PayloadEditor: Prepared file data:', {
            name: fileData.name,
            size: fileData.size,
            type: fileData.type,
            contentLength: fileData.content.length
          });

          // Find the form and get the phx-target
          const form = fileInput.closest('form');
          if (form) {
            const target = form.getAttribute('phx-target');
            console.log('PayloadEditor: Pushing upload_file event to target:', target);

            // Push event to the specific LiveView component using pushEventTo
            this.pushEventTo(target, "upload_file", { file_data: fileData });
          } else {
            console.error('PayloadEditor: Could not find form element');
          }
        };

        reader.onerror = () => {
          console.error('PayloadEditor: Error reading file');
          // Could show an error message here
        };

        reader.readAsDataURL(file);
      });
    });
  }
};

// CSS for syntax highlighting (to be added to app.css)
const syntaxHighlightingCSS = `
.template-syntax {
  background: linear-gradient(90deg, 
    rgba(59, 130, 246, 0.05) 0%, 
    rgba(59, 130, 246, 0.02) 100%);
  border-left: 3px solid rgba(59, 130, 246, 0.3);
}

.template-syntax:focus {
  border-left-color: rgba(59, 130, 246, 0.6);
}
`;

// Auto-inject CSS if not already present
if (!document.querySelector('#payload-editor-styles')) {
  const style = document.createElement('style');
  style.id = 'payload-editor-styles';
  style.textContent = syntaxHighlightingCSS;
  document.head.appendChild(style);
}
