defmodule MqttableWeb.EnhancedPayloadEditorComponent do
  @moduledoc """
  Enhanced payload editor with advanced template helper functionality.

  Features:
  - Search and filter functions
  - Categorized function display
  - Expandable "show more" functionality
  - Support for both prefixed and clean function names
  """

  use MqttableWeb, :live_component
  alias Mqttable.Templating.Engine

  require Logger

  @impl true
  def mount(socket) do
    socket =
      socket
      |> assign_new(:payload, fn -> "" end)
      |> assign_new(:payload_format, fn -> "text" end)
      |> assign_new(:show_helper, fn -> false end)
      |> assign_new(:selected_category, fn -> "all" end)
      |> assign_new(:show_all_functions, fn -> false end)
      |> assign_new(:favorite_functions, fn -> MapSet.new() end)
      |> assign_new(:advanced_groups_expanded, fn -> %{} end)
      |> assign_new(:uploaded_file, fn -> nil end)
      |> assign_new(:file_preview, fn -> nil end)
      |> assign_new(:file_upload_error, fn -> nil end)

    {:ok, socket}
  end

  @impl true
  def update(assigns, socket) do
    # Get payload and format from assigns or existing socket
    payload = Map.get(assigns, :payload, socket.assigns[:payload] || "")
    payload_format = Map.get(assigns, :payload_format, socket.assigns[:payload_format] || "text")

    # Initialize component state only if not already set
    socket =
      socket
      |> assign_new(:selected_category, fn -> "all" end)
      |> assign_new(:show_all_functions, fn -> false end)
      |> assign_new(:show_helper, fn -> false end)
      |> assign_new(:advanced_groups_expanded, fn -> %{} end)
      |> assign_new(:uploaded_file, fn -> nil end)
      |> assign_new(:file_preview, fn -> nil end)
      |> assign_new(:file_upload_error, fn -> nil end)

    # Update payload and format
    socket =
      socket
      |> assign(:payload, payload)
      |> assign(:payload_format, payload_format)
      |> assign(:preview_result, generate_preview(payload))

    # Handle file upload assigns from parent LiveView
    socket =
      if Map.has_key?(assigns, :uploaded_file) do
        socket
        |> assign(:uploaded_file, assigns.uploaded_file)
        |> assign(:file_preview, Map.get(assigns, :file_preview))
        |> assign(:file_upload_error, Map.get(assigns, :file_upload_error))
        |> then(fn socket ->
          # If we have a successful upload, notify parent about payload change
          if assigns.uploaded_file && !assigns.file_upload_error do
            send(self(), {:payload_editor_changed, assigns.payload, assigns.payload_format})
          end

          socket
        end)
      else
        # Preserve existing file upload state if not explicitly updated
        socket
        |> assign(:uploaded_file, socket.assigns[:uploaded_file])
        |> assign(:file_preview, socket.assigns[:file_preview])
        |> assign(:file_upload_error, socket.assigns[:file_upload_error])
      end

    # Handle file data from send_update (from SendMessageModalComponent)
    socket =
      if Map.has_key?(assigns, :file_data) do
        require Logger

        Logger.debug(
          "EnhancedPayloadEditorComponent: Processing file data: #{assigns.file_data["name"]}"
        )

        case process_file_data(assigns.file_data) do
          {:ok, file_info} ->
            # Generate preview for images
            file_preview =
              if String.starts_with?(file_info.type, "image/") do
                "data:#{file_info.type};base64,#{file_info.content}"
              else
                nil
              end

            # Update socket with file information
            socket =
              socket
              |> assign(:uploaded_file, file_info)
              |> assign(:file_preview, file_preview)
              |> assign(:payload, file_info.content)
              |> assign(:payload_format, "file")
              |> assign(:file_upload_error, nil)

            # Notify parent component
            send(self(), {:payload_editor_changed, file_info.content, "file"})

            socket

          {:error, error_message} ->
            socket
            |> assign(:file_upload_error, error_message)
            |> assign(:uploaded_file, nil)
            |> assign(:file_preview, nil)
        end
      else
        socket
      end

    # Assign other non-conflicting assigns
    other_assigns =
      Map.drop(assigns, [
        :payload,
        :payload_format,
        :selected_category,
        :show_all_functions,
        :show_helper,
        :advanced_groups_expanded,
        :uploaded_file,
        :file_preview,
        :file_upload_error
      ])

    socket = assign(socket, other_assigns)

    {:ok, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div
      class="form-control w-full"
      phx-hook="PayloadEditor"
      id={"payload-editor-container-#{@myself}"}
    >
      <label class="label">
        <span class="label-text font-medium">{Map.get(assigns, :label, "Payload")}</span>
      </label>
      
    <!-- Format Selection -->
      <div class="flex items-center gap-3 mb-3">
        <div class="join">
          <input
            type="radio"
            name={"format-#{@myself}"}
            value="text"
            class="join-item btn btn-sm"
            aria-label="Text"
            checked={@payload_format == "text"}
            phx-click="format_changed"
            phx-value-format="text"
            phx-target={@myself}
          />
          <input
            type="radio"
            name={"format-#{@myself}"}
            value="json"
            class="join-item btn btn-sm"
            aria-label="JSON"
            checked={@payload_format == "json"}
            phx-click="format_changed"
            phx-value-format="json"
            phx-target={@myself}
          />
          <input
            type="radio"
            name={"format-#{@myself}"}
            value="hex"
            class="join-item btn btn-sm"
            aria-label="Hex"
            checked={@payload_format == "hex"}
            phx-click="format_changed"
            phx-value-format="hex"
            phx-target={@myself}
          />
          <input
            type="radio"
            name={"format-#{@myself}"}
            value="file"
            class="join-item btn btn-sm"
            aria-label="File"
            checked={@payload_format == "file"}
            phx-click="format_changed"
            phx-value-format="file"
            phx-target={@myself}
          />
        </div>
      </div>
      
    <!-- Payload Input -->
      <%= if @payload_format == "file" do %>
        <!-- File Upload Form -->
        <form phx-submit="upload_file" phx-change="file_selected" phx-target={@myself}>
          <!-- File Upload Area with Drag & Drop -->
          <section class="border-2 border-dashed border-base-300 rounded-lg p-6 text-center transition-colors hover:border-primary">
            <%= if @uploaded_file do %>
              <!-- File Information Display -->
              <div class="space-y-4">
                <div class="flex items-center justify-center gap-2 text-success">
                  <.icon name="hero-document-check" class="size-6" />
                  <span class="font-medium">File uploaded successfully</span>
                </div>

                <div class="bg-base-200 rounded-lg p-4 space-y-2">
                  <div class="flex items-center justify-between">
                    <span class="text-sm font-medium">File:</span>
                    <span class="text-sm">{@uploaded_file.name}</span>
                  </div>
                  <div class="flex items-center justify-between">
                    <span class="text-sm font-medium">Size:</span>
                    <span class="text-sm">{format_file_size(@uploaded_file.size)}</span>
                  </div>
                  <div class="flex items-center justify-between">
                    <span class="text-sm font-medium">Type:</span>
                    <span class="text-sm">{@uploaded_file.type}</span>
                  </div>
                </div>
                
    <!-- Image Preview -->
                <%= if @file_preview do %>
                  <div class="mt-4">
                    <div class="text-sm font-medium mb-2">Preview:</div>
                    <img
                      src={@file_preview}
                      alt="File preview"
                      class="max-w-full max-h-48 mx-auto rounded-lg border border-base-300"
                    />
                  </div>
                <% end %>

                <button
                  type="button"
                  class="btn btn-sm btn-outline"
                  phx-click="clear_file"
                  phx-target={@myself}
                >
                  <.icon name="hero-x-mark" class="size-4 mr-1" /> Clear File
                </button>
              </div>
            <% else %>
              <!-- File Upload Input -->
              <div class="space-y-4">
                <.icon name="hero-cloud-arrow-up" class="size-12 mx-auto text-base-content/40" />
                <div>
                  <p class="text-lg font-medium">Drop files here or click to browse</p>
                  <p class="text-sm text-base-content/60 mt-1">
                    Supports all file types. Files will be converted to base64 for MQTT transmission.
                  </p>
                </div>

                <input
                  type="file"
                  id={"file-input-#{@myself}"}
                  class="file-input file-input-bordered file-input-primary w-full max-w-xs"
                  phx-change="file_selected"
                  phx-target={@myself}
                />
              </div>
            <% end %>
            
    <!-- File Upload Errors -->
            <%= if @file_upload_error do %>
              <p class="alert alert-error mt-3">{@file_upload_error}</p>
            <% end %>
          </section>
        </form>
      <% else %>
        <!-- Regular Textarea -->
        <textarea
          id={"payload-editor-#{@myself}"}
          name="payload"
          placeholder={get_placeholder(@payload_format)}
          class="textarea textarea-bordered w-full h-32 payload-textarea font-mono"
          phx-change="payload_changed"
          phx-target={@myself}
        ><%= @payload %></textarea>
      <% end %>
      
    <!-- Live Preview -->
      <%= if @show_helper and (String.contains?(@payload, "{{") || String.contains?(@payload, "{%")) do %>
        <div class="mt-3">
          <div class="text-sm font-medium mb-2 flex items-center gap-2">
            <.icon name="hero-eye" class="size-4" /> Live Preview:
          </div>
          <div class="bg-base-100 border border-base-300 rounded-lg p-3 text-sm font-mono preview-content">
            <%= case @preview_result do %>
              <% {:ok, result} -> %>
                <pre class="whitespace-pre-wrap text-success"><%= result %></pre>
              <% {:error, error} -> %>
                <div class="text-error">Error: {error}</div>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  # Event Handlers

  @impl true
  def handle_event("payload_changed", %{"value" => payload}, socket) do
    socket =
      socket
      |> assign(:payload, payload)
      |> assign(:preview_result, generate_preview(payload))

    # Notify parent component
    send(self(), {:payload_editor_changed, payload, socket.assigns.payload_format})

    {:noreply, socket}
  end

  @impl true
  def handle_event("payload_changed", %{"payload" => payload}, socket) do
    # Handle alternative event format from textarea
    socket =
      socket
      |> assign(:payload, payload)
      |> assign(:preview_result, generate_preview(payload))

    # Notify parent component
    send(self(), {:payload_editor_changed, payload, socket.assigns.payload_format})

    {:noreply, socket}
  end

  @impl true
  def handle_event("format_changed", %{"format" => format}, socket) do
    socket =
      socket
      |> assign(:payload_format, format)
      |> then(fn socket ->
        # Only clear file upload state when switching away from file format
        # Don't clear when switching TO file format
        if format != "file" and socket.assigns[:payload_format] == "file" do
          socket
          |> assign(:uploaded_file, nil)
          |> assign(:file_preview, nil)
          |> assign(:file_upload_error, nil)
          |> assign(:payload, "")
        else
          socket
        end
      end)

    # Notify parent component
    send(self(), {:payload_editor_changed, socket.assigns.payload, format})

    {:noreply, socket}
  end

  @impl true
  def handle_event("toggle_helper", _params, socket) do
    {:noreply, assign(socket, :show_helper, !socket.assigns.show_helper)}
  end

  @impl true
  def handle_event("filter_category", %{"category" => category}, socket) do
    {:noreply, assign(socket, :selected_category, category)}
  end

  @impl true
  def handle_event("filter_category", %{"value" => category}, socket) do
    {:noreply, assign(socket, :selected_category, category)}
  end

  @impl true
  def handle_event("filter_category", params, socket) do
    # Fallback handler for malformed events
    IO.inspect(params, label: "Unexpected filter_category params")
    {:noreply, socket}
  end

  @impl true
  def handle_event("toggle_show_all", _params, socket) do
    {:noreply, assign(socket, :show_all_functions, !socket.assigns.show_all_functions)}
  end

  @impl true
  def handle_event("toggle_advanced_group", %{"group" => group_key}, socket) do
    current_expanded = socket.assigns.advanced_groups_expanded

    new_expanded =
      Map.put(current_expanded, group_key, !Map.get(current_expanded, group_key, false))

    {:noreply, assign(socket, :advanced_groups_expanded, new_expanded)}
  end

  @impl true
  def handle_event("insert_template", %{"template" => template}, socket) do
    # Push event to JavaScript to insert at cursor position
    {:noreply,
     push_event(socket, "insert_at_cursor", %{
       target_id: "payload-editor-#{socket.assigns.myself}",
       text: template
     })}
  end

  @impl true
  def handle_event("toggle_favorite", %{"function" => function_name}, socket) do
    favorites = socket.assigns.favorite_functions

    new_favorites =
      if function_name in favorites do
        MapSet.delete(favorites, function_name)
      else
        MapSet.put(favorites, function_name)
      end

    {:noreply, assign(socket, :favorite_functions, new_favorites)}
  end

  @impl true
  def handle_event("file_selected", params, socket) do
    require Logger

    Logger.debug(
      "EnhancedPayloadEditorComponent: file_selected event received with params: #{inspect(params)}"
    )

    # Check if we have file data from JavaScript hook
    case Map.get(params, "file_data") do
      nil ->
        # No file data yet, JavaScript will handle the file reading
        {:noreply, socket}

      file_data ->
        # File data received from JavaScript, process it
        handle_event("upload_file", %{"file_data" => file_data}, socket)
    end
  end

  @impl true
  def handle_event("upload_file", %{"file_data" => file_data}, socket) do
    # Process the file data sent from JavaScript
    case process_file_data(file_data) do
      {:ok, file_info} ->
        # Generate preview for images
        file_preview =
          if String.starts_with?(file_info.type, "image/") do
            "data:#{file_info.type};base64,#{file_info.content}"
          else
            nil
          end

        # Update socket with file information
        socket =
          socket
          |> assign(:uploaded_file, file_info)
          |> assign(:file_preview, file_preview)
          |> assign(:payload, file_info.content)
          |> assign(:payload_format, "file")
          |> assign(:file_upload_error, nil)

        # Notify parent component
        send(self(), {:payload_editor_changed, file_info.content, "file"})

        {:noreply, socket}

      {:error, error_message} ->
        socket =
          socket
          |> assign(:file_upload_error, error_message)
          |> assign(:uploaded_file, nil)
          |> assign(:file_preview, nil)

        {:noreply, socket}
    end
  end

  @impl true
  def handle_event("upload_file", _params, socket) do
    # Handle form submission without file data
    {:noreply, assign(socket, :file_upload_error, "No file selected")}
  end

  @impl true
  def handle_event("clear_file", _params, socket) do
    socket =
      socket
      |> assign(:uploaded_file, nil)
      |> assign(:file_preview, nil)
      |> assign(:payload, "")
      |> assign(:file_upload_error, nil)

    # Notify parent component
    send(self(), {:payload_editor_changed, "", "file"})

    {:noreply, socket}
  end

  @impl true
  def handle_event("clear_file_error", _params, socket) do
    {:noreply, assign(socket, :file_upload_error, nil)}
  end

  # Helper Functions

  defp process_file_data(%{"name" => name, "size" => size, "type" => type, "content" => content}) do
    file_info = %{
      name: name,
      size: size,
      type: type,
      content: content
    }

    # Validate the file
    with {:ok, _} <- validate_file_size(size),
         {:ok, _} <- validate_file_type(type),
         {:ok, _} <- validate_file_name(name),
         {:ok, _} <- validate_base64_content(content) do
      {:ok, file_info}
    else
      {:error, reason} -> {:error, reason}
    end
  end

  defp process_file_data(_), do: {:error, "Invalid file data"}

  defp error_to_string(:too_large), do: "File too large"
  defp error_to_string(:not_accepted), do: "File type not accepted"
  defp error_to_string(:too_many_files), do: "Too many files selected"
  defp error_to_string(error), do: "Upload error: #{inspect(error)}"

  defp generate_preview(payload) do
    if String.contains?(payload, "{{") || String.contains?(payload, "{%") do
      case Engine.preview(payload) do
        {:ok, result} -> {:ok, result}
        {:error, error} -> {:error, error}
      end
    else
      {:ok, payload}
    end
  end

  defp get_placeholder("json"), do: "Enter JSON payload or use {{ template_function }} syntax"
  defp get_placeholder("hex"), do: "Enter hex payload (e.g., 48656C6C6F)"
  defp get_placeholder("file"), do: "Upload a file to use as payload"
  defp get_placeholder(_), do: "Enter payload or use {{ template_function }} syntax"

  defp format_file_size(size) when is_integer(size) do
    cond do
      size >= 1_048_576 -> "#{Float.round(size / 1_048_576, 2)} MB"
      size >= 1_024 -> "#{Float.round(size / 1_024, 2)} KB"
      true -> "#{size} B"
    end
  end

  defp format_file_size(_), do: "Unknown size"

  # File upload validation
  defp validate_file_upload(file_info) do
    with {:ok, _} <- validate_file_size(file_info.size),
         {:ok, _} <- validate_file_type(file_info.type),
         {:ok, _} <- validate_file_name(file_info.name),
         {:ok, _} <- validate_base64_content(file_info.content) do
      {:ok, file_info}
    else
      {:error, reason} -> {:error, reason}
    end
  end

  # Maximum file size for MQTT (16MB as base64 will be ~33% larger)
  @max_file_size 16 * 1024 * 1024

  defp validate_file_size(size) when is_integer(size) and size > 0 and size <= @max_file_size do
    {:ok, size}
  end

  defp validate_file_size(size) when is_integer(size) and size > @max_file_size do
    {:error,
     "File size (#{format_file_size(size)}) exceeds maximum limit of #{format_file_size(@max_file_size)}"}
  end

  defp validate_file_size(_) do
    {:error, "Invalid file size"}
  end

  defp validate_file_type(type) when is_binary(type) and byte_size(type) > 0 do
    {:ok, type}
  end

  defp validate_file_type(_) do
    {:error, "Invalid file type"}
  end

  defp validate_file_name(name) when is_binary(name) and byte_size(name) > 0 do
    # Basic filename validation
    if String.length(name) > 255 do
      {:error, "Filename too long (maximum 255 characters)"}
    else
      {:ok, name}
    end
  end

  defp validate_file_name(_) do
    {:error, "Invalid filename"}
  end

  defp validate_base64_content(content) when is_binary(content) do
    require Logger

    Logger.debug(
      "Validating base64 content, length: #{String.length(content)}, first 50 chars: #{String.slice(content, 0, 50)}"
    )

    # Check if content is empty
    if String.length(content) == 0 do
      Logger.debug("Base64 content is empty")
      {:error, "File content is empty"}
    else
      # Try to validate base64 with padding if needed
      padded_content =
        case rem(String.length(content), 4) do
          0 -> content
          1 -> content <> "==="
          2 -> content <> "=="
          3 -> content <> "="
        end

      case Base.decode64(padded_content) do
        {:ok, _decoded} ->
          Logger.debug("Base64 validation successful")
          {:ok, content}

        :error ->
          Logger.debug("Base64 validation failed even with padding")
          # Try without strict validation
          case Base.decode64(content, ignore: :whitespace) do
            {:ok, _decoded} ->
              Logger.debug("Base64 validation successful with ignore whitespace")
              {:ok, content}

            :error ->
              Logger.debug("Base64 validation failed completely")
              {:error, "Invalid file content encoding"}
          end
      end
    end
  end

  defp validate_base64_content(_) do
    {:error, "Invalid file content"}
  end

  # Function categorization and display logic

  defp get_advanced_groups do
    %{
      "data_processing" => %{
        title: "Data Processing",
        icon: "📊",
        functions: [
          %{
            name: "base64_encode",
            display_name: "Base64 Encode",
            icon: "🔐",
            category: "advanced",
            description: "Encode data to Base64 format"
          },
          %{
            name: "base64_decode",
            display_name: "Base64 Decode",
            icon: "🔓",
            category: "advanced",
            description: "Decode Base64 data"
          },
          %{
            name: "json_encode",
            display_name: "JSON Encode",
            icon: "📄",
            category: "advanced",
            description: "Convert data to JSON format"
          },
          %{
            name: "json_decode",
            display_name: "JSON Decode",
            icon: "📋",
            category: "advanced",
            description: "Parse JSON data"
          },
          %{
            name: "hash",
            display_name: "Hash",
            icon: "🔒",
            category: "advanced",
            description: "Generate hash (MD5, SHA1, SHA256)"
          },
          %{
            name: "url_encode",
            display_name: "URL Encode",
            icon: "🔗",
            category: "advanced",
            description: "URL encode data"
          },
          %{
            name: "url_decode",
            display_name: "URL Decode",
            icon: "🔓",
            category: "advanced",
            description: "URL decode data"
          }
        ]
      },
      "text_processing" => %{
        title: "Text Processing",
        icon: "📝",
        functions: [
          %{
            name: "capitalize",
            display_name: "Capitalize",
            icon: "🔤",
            category: "advanced",
            description: "Capitalize first letter"
          },
          %{
            name: "uppercase",
            display_name: "Uppercase",
            icon: "🔠",
            category: "advanced",
            description: "Convert to uppercase"
          },
          %{
            name: "lowercase",
            display_name: "Lowercase",
            icon: "🔡",
            category: "advanced",
            description: "Convert to lowercase"
          },
          %{
            name: "truncate",
            display_name: "Truncate",
            icon: "✂️",
            category: "advanced",
            description: "Truncate text to length"
          },
          %{
            name: "pad_left",
            display_name: "Pad Left",
            icon: "⬅️",
            category: "advanced",
            description: "Pad text on the left"
          },
          %{
            name: "pad_right",
            display_name: "Pad Right",
            icon: "➡️",
            category: "advanced",
            description: "Pad text on the right"
          }
        ]
      },
      "math_functions" => %{
        title: "Math Functions",
        icon: "🧮",
        functions: [
          %{
            name: "round",
            display_name: "Round",
            icon: "🔄",
            category: "advanced",
            description: "Round number to precision"
          },
          %{
            name: "ceil",
            display_name: "Ceiling",
            icon: "⬆️",
            category: "advanced",
            description: "Round up to integer"
          },
          %{
            name: "floor",
            display_name: "Floor",
            icon: "⬇️",
            category: "advanced",
            description: "Round down to integer"
          },
          %{
            name: "abs",
            display_name: "Absolute",
            icon: "📏",
            category: "advanced",
            description: "Absolute value"
          },
          %{
            name: "min",
            display_name: "Minimum",
            icon: "⬇️",
            category: "advanced",
            description: "Minimum of two numbers"
          },
          %{
            name: "max",
            display_name: "Maximum",
            icon: "⬆️",
            category: "advanced",
            description: "Maximum of two numbers"
          },
          %{
            name: "clamp",
            display_name: "Clamp",
            icon: "📐",
            category: "advanced",
            description: "Clamp value between min/max"
          }
        ]
      }
    }
  end

  defp get_all_functions do
    [
      # IoT & Sensors
      %{
        name: "temperature",
        display_name: "Temperature",
        icon: "🌡️",
        category: "iot",
        description: "Random temperature value"
      },
      %{
        name: "humidity",
        display_name: "Humidity",
        icon: "💧",
        category: "iot",
        description: "Random humidity percentage"
      },
      %{
        name: "pressure",
        display_name: "Pressure",
        icon: "📊",
        category: "iot",
        description: "Random pressure value"
      },
      %{
        name: "battery_level",
        display_name: "Battery",
        icon: "🔋",
        category: "iot",
        description: "Random battery level"
      },
      %{
        name: "device_id",
        display_name: "Device ID",
        icon: "📱",
        category: "iot",
        description: "Random device identifier"
      },
      %{
        name: "device_status",
        display_name: "Status",
        icon: "📊",
        category: "iot",
        description: "Random device status"
      },
      %{
        name: "signal_strength",
        display_name: "Signal",
        icon: "📶",
        category: "iot",
        description: "Random signal strength"
      },

      # Person Data (clean names without fake_ prefix)
      %{
        name: "name",
        display_name: "Name",
        icon: "👤",
        category: "person",
        description: "Random full name"
      },
      %{
        name: "first_name",
        display_name: "First Name",
        icon: "👤",
        category: "person",
        description: "Random first name"
      },
      %{
        name: "last_name",
        display_name: "Last Name",
        icon: "👤",
        category: "person",
        description: "Random last name"
      },
      %{
        name: "title",
        display_name: "Title",
        icon: "💼",
        category: "person",
        description: "Random professional title"
      },
      %{
        name: "email",
        display_name: "Email",
        icon: "📧",
        category: "person",
        description: "Random email address"
      },
      %{
        name: "username",
        display_name: "Username",
        icon: "👤",
        category: "person",
        description: "Random username"
      },

      # Address & Location
      %{
        name: "address",
        display_name: "Address",
        icon: "🏠",
        category: "address",
        description: "Random street address"
      },
      %{
        name: "city",
        display_name: "City",
        icon: "🏙️",
        category: "address",
        description: "Random city name"
      },
      %{
        name: "country",
        display_name: "Country",
        icon: "🌍",
        category: "address",
        description: "Random country name"
      },
      %{
        name: "state",
        display_name: "State",
        icon: "🗺️",
        category: "address",
        description: "Random state name"
      },
      %{
        name: "postcode",
        display_name: "Postcode",
        icon: "📮",
        category: "address",
        description: "Random postal code"
      },
      %{
        name: "latitude",
        display_name: "Latitude",
        icon: "🌐",
        category: "address",
        description: "Random latitude"
      },
      %{
        name: "longitude",
        display_name: "Longitude",
        icon: "🌐",
        category: "address",
        description: "Random longitude"
      },
      %{
        name: "timezone",
        display_name: "Timezone",
        icon: "🕐",
        category: "address",
        description: "Random timezone"
      },

      # Company & Business
      %{
        name: "company",
        display_name: "Company",
        icon: "🏢",
        category: "company",
        description: "Random company name"
      },
      %{
        name: "buzzword",
        display_name: "Buzzword",
        icon: "💡",
        category: "company",
        description: "Random business buzzword"
      },
      %{
        name: "catch_phrase",
        display_name: "Catch Phrase",
        icon: "💬",
        category: "company",
        description: "Random marketing phrase"
      },
      %{
        name: "department",
        display_name: "Department",
        icon: "🏬",
        category: "company",
        description: "Random department name"
      },

      # Internet & Tech
      %{
        name: "ipv4",
        display_name: "IPv4",
        icon: "🌐",
        category: "internet",
        description: "Random IPv4 address"
      },
      %{
        name: "ipv6",
        display_name: "IPv6",
        icon: "🌐",
        category: "internet",
        description: "Random IPv6 address"
      },
      %{
        name: "domain",
        display_name: "Domain",
        icon: "🌐",
        category: "internet",
        description: "Random domain name"
      },
      %{
        name: "url",
        display_name: "URL",
        icon: "🔗",
        category: "internet",
        description: "Random URL"
      },
      %{
        name: "mac_address",
        display_name: "MAC Address",
        icon: "🔌",
        category: "internet",
        description: "Random MAC address"
      },

      # Commerce & Products
      %{
        name: "product_name",
        display_name: "Product",
        icon: "📦",
        category: "commerce",
        description: "Random product name"
      },
      %{
        name: "price",
        display_name: "Price",
        icon: "💰",
        category: "commerce",
        description: "Random price"
      },
      %{
        name: "color_name",
        display_name: "Color",
        icon: "🎨",
        category: "commerce",
        description: "Random color name"
      },
      %{
        name: "vehicle",
        display_name: "Vehicle",
        icon: "🚗",
        category: "commerce",
        description: "Random vehicle"
      },
      %{
        name: "dish",
        display_name: "Dish",
        icon: "🍽️",
        category: "commerce",
        description: "Random dish name"
      },

      # Text & Content
      %{
        name: "sentence",
        display_name: "Sentence",
        icon: "📝",
        category: "text",
        description: "Random sentence"
      },
      %{
        name: "paragraph",
        display_name: "Paragraph",
        icon: "📄",
        category: "text",
        description: "Random paragraph"
      },
      %{
        name: "word",
        display_name: "Word",
        icon: "📝",
        category: "text",
        description: "Random word"
      },

      # Other/Utility
      %{
        name: "uuid",
        display_name: "UUID",
        icon: "🆔",
        category: "other",
        description: "Random UUID"
      },
      %{
        name: "iso8601",
        display_name: "Timestamp",
        icon: "🕐",
        category: "other",
        description: "Current timestamp"
      },
      %{
        name: "random_int",
        display_name: "Random Number",
        icon: "🎲",
        category: "other",
        description: "Random integer"
      },
      %{
        name: "random_bool",
        display_name: "Random Bool",
        icon: "🎯",
        category: "other",
        description: "Random boolean"
      },
      %{
        name: "currency_code",
        display_name: "Currency",
        icon: "💱",
        category: "other",
        description: "Random currency code"
      },
      %{
        name: "bitcoin_address",
        display_name: "Bitcoin",
        icon: "₿",
        category: "other",
        description: "Random Bitcoin address"
      },
      %{
        name: "ethereum_address",
        display_name: "Ethereum",
        icon: "Ξ",
        category: "other",
        description: "Random Ethereum address"
      }
    ]
  end

  defp get_common_functions do
    # Most commonly used functions (displayed by default)
    [
      "temperature",
      "humidity",
      "device_id",
      "uuid",
      "iso8601",
      "random_int",
      "name",
      "email",
      "company",
      "city",
      "ipv4",
      "product_name"
    ]
  end

  defp get_filtered_functions(category) do
    all_functions = get_all_functions()
    filter_by_category(all_functions, category)
  end

  defp get_displayed_functions(category, show_all) do
    # For advanced category, return empty list since functions are shown in accordion
    if category == "advanced" do
      []
    else
      filtered = get_filtered_functions(category)

      if show_all or category != "all" do
        filtered
      else
        common_names = get_common_functions()
        Enum.filter(filtered, fn func -> func.name in common_names end)
      end
    end
  end

  defp filter_by_category(functions, "all"), do: functions

  defp filter_by_category(functions, category) do
    Enum.filter(functions, fn func -> func.category == category end)
  end

  # Example templates
  defp sensor_data_example do
    """
    {
      "device_id": "{{ device_id }}",
      "timestamp": "{{ iso8601 }}",
      "temperature": {{ temperature }},
      "humidity": {{ humidity }},
      "battery": {{ battery_level }}
    }
    """
  end

  defp user_profile_example do
    """
    {
      "name": "{{ name }}",
      "email": "{{ email }}",
      "company": "{{ company }}",
      "address": {
        "city": "{{ city }}",
        "country": "{{ country }}"
      }
    }
    """
  end

  defp device_status_example do
    """
    Device {{ device_id }} is {{ device_status }}.
    Uptime: {{ uptime }} seconds
    Firmware: {{ firmware_version }}
    Last seen: {{ iso8601 }}
    """
  end
end
